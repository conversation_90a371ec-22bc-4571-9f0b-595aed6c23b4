package com.example.config;

import com.example.exception.PhotoException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;

/**
 * 全局异常处理器
 * 用于统一处理应用程序中抛出的各种异常，提供统一的错误响应格式
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 处理照片相关的业务异常
     * @param ex PhotoException 照片业务异常
     * @return ResponseEntity<ErrorResponse> 包含错误信息的HTTP响应
     */
    @ExceptionHandler(PhotoException.class)
    public ResponseEntity<ErrorResponse> handlePhotoException(PhotoException ex) {
        // 记录错误日志
        log.error("PhotoException occurred: {}", ex.getMessage(), ex);
        // 返回400状态码和错误详情
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new ErrorResponse(ex.getCode(), ex.getMessage(), LocalDateTime.now()));
    }

    /**
     * 处理所有未被特定处理器捕获的异常
     * @param ex Exception 通用异常
     * @return ResponseEntity<ErrorResponse> 包含通用错误信息的HTTP响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception ex) {
        // 记录未预期的错误日志
        log.error("Unexpected error occurred", ex);
        // 返回500状态码和通用错误信息
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("INTERNAL_ERROR", "An unexpected error occurred", LocalDateTime.now()));
    }

    /**
     * 错误响应数据传输对象
     * 用于统一错误响应的格式
     */
    private static class ErrorResponse {
        /** 错误代码 */
        private final String code;
        /** 错误消息 */
        private final String message;
        /** 错误发生时间戳 */
        private final LocalDateTime timestamp;

        /**
         * 构造函数
         * @param code 错误代码
         * @param message 错误消息
         * @param timestamp 错误发生时间戳
         */
        public ErrorResponse(String code, String message, LocalDateTime timestamp) {
            this.code = code;
            this.message = message;
            this.timestamp = timestamp;
        }

        // Getter方法用于JSON序列化
        public String getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }
    }
}
